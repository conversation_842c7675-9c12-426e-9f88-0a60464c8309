{"tests/test_agents.py::TestWorkflowIntegration::test_run_financial_analysis_workflow": true, "tests/test_agents.py::TestErrorHandling::test_empty_dataframe_handling": true, "tests/test_backend.py::TestClient": true, "tests/test_backend.py::TestAPI": true, "tests/test_backend.py::TestExcelProcessor": true, "tests/test_backend.py::TestFinancialAnalyzer": true, "tests/test_backend.py::TestDataValidation": true}