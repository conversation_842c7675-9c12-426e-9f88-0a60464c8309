"""
Financial analysis service that orchestrates the Lang<PERSON>hain agents.
"""

import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime
import logging
import time

from ..agents.pandas_agents import run_financial_analysis
from .excel_processor import ExcelProcessor

# Import shared schemas
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from shared.schemas import (
    FinancialAnalysisResult, SupplierSummary, VoucherSummary,
    MonthlyTrend, AnalysisRequest
)

logger = logging.getLogger(__name__)


class FinancialAnalyzer:
    """Main service for financial analysis using LangChain agents."""
    
    def __init__(self):
        self.excel_processor = ExcelProcessor()
    
    def analyze_file(self, file_content: bytes, filename: str,
                    config: Optional[Dict] = None) -> FinancialAnalysisResult:
        """
        Perform complete financial analysis on uploaded Excel file.

        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            config: Analysis configuration parameters

        Returns:
            FinancialAnalysisResult with complete analysis
        """
        start_time = time.time()

        try:
            # Process Excel file
            logger.info(f"Processing Excel file: {filename}")
            df = self.excel_processor.process_file(file_content, filename)

            # Set default configuration
            if config is None:
                config = {
                    "assume_cost_percentage": 70.0,
                    "low_margin_threshold": 10.0
                }

            # Check if file is too large and needs batch processing
            if len(df) > 500:  # More than 500 records
                logger.info(f"Large file detected ({len(df)} records). Processing in batches...")
                analysis_results = self._analyze_in_batches(df, config)
            else:
                # Run LangChain analysis workflow for smaller files
                logger.info("Running financial analysis workflow")
                analysis_results = run_financial_analysis(df, config)
            
            # Extract structured data from analysis results
            structured_results = self._extract_structured_results(
                df, analysis_results, config
            )
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Create final result
            result = FinancialAnalysisResult(
                file_name=filename,
                total_records_processed=len(df),
                total_gross_amount=float(df['gross_amount'].sum()),
                total_suppliers=df['supplier'].nunique(),
                total_vouchers=df['voucher'].nunique(),
                supplier_summaries=structured_results['supplier_summaries'],
                voucher_summaries=structured_results['voucher_summaries'],
                monthly_trends=structured_results['monthly_trends'],
                low_margin_transactions=structured_results['low_margin_transactions'],
                negative_margin_transactions=structured_results['negative_margin_transactions'],
                final_report=analysis_results.get('final_report', ''),
                assumptions_used=analysis_results['assumptions'],
                warnings=analysis_results['warnings'],
                processing_time_seconds=processing_time
            )
            
            logger.info(f"Analysis completed in {processing_time:.2f} seconds")
            return result
            
        except Exception as e:
            logger.error(f"Error in financial analysis: {str(e)}")
            raise

    def _analyze_in_batches(self, df: pd.DataFrame, config: Dict) -> Dict:
        """
        Analyze large datasets in smaller batches to avoid API limits.

        Args:
            df: Large DataFrame to process
            config: Analysis configuration

        Returns:
            Combined analysis results
        """
        batch_size = 200  # Process 200 records at a time
        total_batches = (len(df) + batch_size - 1) // batch_size

        logger.info(f"Processing {len(df)} records in {total_batches} batches of {batch_size}")

        # Initialize combined results
        combined_results = {
            'final_report': "",
            'supplier_analysis': "",
            'margin_analysis': "",
            'trend_analysis': "",
            'assumptions': [],
            'warnings': []
        }

        # Process each batch
        for i in range(total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(df))
            batch_df = df.iloc[start_idx:end_idx].copy()

            logger.info(f"Processing batch {i+1}/{total_batches} (records {start_idx+1}-{end_idx})")

            try:
                # Process batch with simplified analysis (no AI for large files)
                batch_results = self._analyze_batch_simple(batch_df, config)

                # Combine results
                self._combine_batch_results(combined_results, batch_results, i+1, total_batches)

            except Exception as e:
                logger.warning(f"Error processing batch {i+1}: {str(e)}")
                combined_results['warnings'].append(f"Batch {i+1} processing failed: {str(e)}")

        # Generate final summary
        combined_results['final_report'] = self._generate_batch_summary(df, combined_results, total_batches)

        return combined_results

    def _analyze_batch_simple(self, batch_df: pd.DataFrame, config: Dict) -> Dict:
        """
        Perform simplified analysis on a batch without AI agents.
        Uses pandas operations only to avoid API limits.
        """
        # Calculate margins
        batch_df = self._calculate_margins(batch_df, config['assume_cost_percentage'])

        # Basic statistics
        supplier_stats = batch_df.groupby('supplier').agg({
            'gross_amount': ['sum', 'count', 'mean'],
            'margin_percentage': 'mean'
        }).round(2)

        # Risk analysis
        low_margin_count = (batch_df['margin_percentage'] < config['low_margin_threshold']).sum()
        negative_margin_count = (batch_df['margin_percentage'] < 0).sum()

        return {
            'supplier_stats': supplier_stats,
            'low_margin_count': int(low_margin_count),
            'negative_margin_count': int(negative_margin_count),
            'total_amount': float(batch_df['gross_amount'].sum()),
            'record_count': len(batch_df)
        }

    def _combine_batch_results(self, combined: Dict, batch_results: Dict, batch_num: int, total_batches: int):
        """Combine results from individual batches."""
        # Add batch info to combined results
        combined['supplier_analysis'] += f"\nBatch {batch_num}/{total_batches}: "
        combined['supplier_analysis'] += f"Processed {batch_results['record_count']} records, "
        combined['supplier_analysis'] += f"Total amount: ${batch_results['total_amount']:,.2f}"

        combined['margin_analysis'] += f"\nBatch {batch_num}: "
        combined['margin_analysis'] += f"Low margin: {batch_results['low_margin_count']}, "
        combined['margin_analysis'] += f"Negative margin: {batch_results['negative_margin_count']}"

    def _generate_batch_summary(self, full_df: pd.DataFrame, combined_results: Dict, total_batches: int) -> str:
        """Generate final summary for batch-processed data."""
        # Calculate overall statistics
        full_df_with_margins = self._calculate_margins(full_df, 70.0)  # Use default assumption

        total_amount = full_df_with_margins['gross_amount'].sum()
        total_suppliers = full_df_with_margins['supplier'].nunique()
        avg_margin = full_df_with_margins['margin_percentage'].mean()
        low_margin_total = (full_df_with_margins['margin_percentage'] < 10).sum()
        negative_margin_total = (full_df_with_margins['margin_percentage'] < 0).sum()

        summary = f"""
FINANCIAL ANALYSIS SUMMARY (Batch Processing)

Dataset Overview:
- Total Records: {len(full_df):,}
- Total Suppliers: {total_suppliers:,}
- Processing Method: {total_batches} batches (due to large dataset)
- Total Gross Amount: ${total_amount:,.2f}

Key Findings:
- Average Margin: {avg_margin:.1f}%
- Low Margin Transactions: {low_margin_total:,} ({(low_margin_total/len(full_df)*100):.1f}%)
- Negative Margin Transactions: {negative_margin_total:,} ({(negative_margin_total/len(full_df)*100):.1f}%)

Top 5 Suppliers by Revenue:
"""

        # Add top suppliers
        top_suppliers = full_df_with_margins.groupby('supplier')['gross_amount'].sum().nlargest(5)
        for i, (supplier, amount) in enumerate(top_suppliers.items(), 1):
            summary += f"\n{i}. {supplier}: ${amount:,.2f}"

        summary += f"""

Processing Notes:
- Large dataset processed in batches to optimize performance
- Detailed AI analysis available for smaller datasets (< 500 records)
- All calculations performed using pandas for accuracy
- Batch processing ensures reliable results for large files

Recommendations:
- Review transactions with negative margins immediately
- Investigate suppliers with consistently low margins
- Consider renegotiating terms with high-volume, low-margin suppliers
"""

        return summary
    
    def _extract_structured_results(self, df: pd.DataFrame, 
                                   analysis_results: Dict, 
                                   config: Dict) -> Dict:
        """Extract structured data from LangChain analysis results."""
        
        # Calculate margins if cost data is available or assumed
        df_with_margins = self._calculate_margins(df, config['assume_cost_percentage'])
        
        # Generate supplier summaries
        supplier_summaries = self._generate_supplier_summaries(df_with_margins)
        
        # Generate voucher summaries
        voucher_summaries = self._generate_voucher_summaries(df_with_margins, config)
        
        # Generate monthly trends
        monthly_trends = self._generate_monthly_trends(df_with_margins)
        
        # Identify problematic transactions
        low_margin_threshold = config['low_margin_threshold']
        low_margin_transactions = [
            vs for vs in voucher_summaries 
            if vs.margin_percentage is not None and vs.margin_percentage < low_margin_threshold
        ]
        
        negative_margin_transactions = [
            vs for vs in voucher_summaries 
            if vs.margin_percentage is not None and vs.margin_percentage < 0
        ]
        
        return {
            'supplier_summaries': supplier_summaries,
            'voucher_summaries': voucher_summaries,
            'monthly_trends': monthly_trends,
            'low_margin_transactions': low_margin_transactions,
            'negative_margin_transactions': negative_margin_transactions
        }
    
    def _calculate_margins(self, df: pd.DataFrame, assume_cost_percentage: float) -> pd.DataFrame:
        """Calculate margins for all transactions."""
        df = df.copy()
        
        # Fill missing costs with assumed percentage
        if 'cost' in df.columns:
            df['cost'] = df['cost'].fillna(df['gross_amount'] * (assume_cost_percentage / 100))
        else:
            df['cost'] = df['gross_amount'] * (assume_cost_percentage / 100)
        
        # Calculate margin percentage
        df['margin_percentage'] = ((df['gross_amount'] - df['cost']) / df['gross_amount'] * 100).round(2)
        
        return df
    
    def _generate_supplier_summaries(self, df: pd.DataFrame) -> List[SupplierSummary]:
        """Generate supplier-level summaries."""
        supplier_groups = df.groupby('supplier').agg({
            'gross_amount': ['sum', 'count'],
            'margin_percentage': 'mean'
        }).round(2)
        
        supplier_groups.columns = ['total_gross_amount', 'total_transactions', 'average_margin']
        
        summaries = []
        for supplier_name, row in supplier_groups.iterrows():
            # Count low and negative margin transactions
            supplier_data = df[df['supplier'] == supplier_name]
            low_margin_count = (supplier_data['margin_percentage'] < 10).sum()
            negative_margin_count = (supplier_data['margin_percentage'] < 0).sum()
            
            summary = SupplierSummary(
                supplier_name=supplier_name,
                total_gross_amount=float(row['total_gross_amount']),
                total_transactions=int(row['total_transactions']),
                average_margin=float(row['average_margin']) if pd.notna(row['average_margin']) else None,
                low_margin_transactions=int(low_margin_count),
                negative_margin_transactions=int(negative_margin_count)
            )
            summaries.append(summary)
        
        # Sort by total gross amount descending
        summaries.sort(key=lambda x: x.total_gross_amount, reverse=True)
        return summaries
    
    def _generate_voucher_summaries(self, df: pd.DataFrame, config: Dict) -> List[VoucherSummary]:
        """Generate voucher-level summaries."""
        summaries = []
        low_margin_threshold = config['low_margin_threshold']
        
        for _, row in df.iterrows():
            margin = row.get('margin_percentage')
            
            summary = VoucherSummary(
                voucher_id=str(row['voucher']),
                supplier_name=str(row['supplier']),
                gross_amount=float(row['gross_amount']),
                cost_amount=float(row['cost']) if pd.notna(row.get('cost')) else None,
                margin_percentage=float(margin) if pd.notna(margin) else None,
                is_low_margin=margin < low_margin_threshold if pd.notna(margin) else False,
                is_negative_margin=margin < 0 if pd.notna(margin) else False,
                transaction_date=row.get('date') if pd.notna(row.get('date')) else None
            )
            summaries.append(summary)
        
        return summaries
    
    def _generate_monthly_trends(self, df: pd.DataFrame) -> List[MonthlyTrend]:
        """Generate monthly trend analysis."""
        if 'date' not in df.columns:
            return []
        
        # Create year-month column
        df['year_month'] = df['date'].dt.to_period('M').astype(str)
        
        # Group by supplier and month
        monthly_groups = df.groupby(['supplier', 'year_month']).agg({
            'gross_amount': 'sum',
            'cost': 'sum',
            'voucher': 'count'
        }).reset_index()
        
        trends = []
        for _, row in monthly_groups.iterrows():
            profit_margin = None
            if pd.notna(row['cost']) and row['gross_amount'] > 0:
                profit_margin = ((row['gross_amount'] - row['cost']) / row['gross_amount'] * 100)
            
            trend = MonthlyTrend(
                year_month=row['year_month'],
                supplier_name=row['supplier'],
                total_gross_amount=float(row['gross_amount']),
                total_cost=float(row['cost']) if pd.notna(row['cost']) else None,
                profit_margin=float(profit_margin) if profit_margin is not None else None,
                transaction_count=int(row['voucher'])
            )
            trends.append(trend)
        
        # Sort by year_month and supplier
        trends.sort(key=lambda x: (x.year_month, x.supplier_name))
        return trends
