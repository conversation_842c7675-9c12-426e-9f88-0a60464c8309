{"version": 3, "file": "static/css/main.06956719.css", "mappings": "AACA,WAGE,iBAAkB,CAFlB,2BAA8B,CAC9B,iBAAkB,CAElB,eAAgB,CAChB,uNAAyJ,CACzJ,gFACF,CAGA,WAGE,iBAAkB,CAFlB,2BAA8B,CAC9B,iBAAkB,CAElB,eAAgB,CAChB,+MAAiJ,CACjJ,+DACF,CAGA,WAGE,iBAAkB,CAFlB,2BAA8B,CAC9B,iBAAkB,CAElB,eAAgB,CAChB,iNAAmJ,CACnJ,oBACF,CAGA,WAGE,iBAAkB,CAFlB,2BAA8B,CAC9B,iBAAkB,CAElB,eAAgB,CAChB,yMAA2I,CAC3I,yBACF,CAGA,WAGE,iBAAkB,CAFlB,2BAA8B,CAC9B,iBAAkB,CAElB,eAAgB,CAChB,mNAAqJ,CACrJ,0JACF,CAGA,WAGE,iBAAkB,CAFlB,2BAA8B,CAC9B,iBAAkB,CAElB,eAAgB,CAChB,iNAAmJ,CACnJ,oIACF,CAGA,WAGE,iBAAkB,CAFlB,2BAA8B,CAC9B,iBAAkB,CAElB,eAAgB,CAChB,yMAA2I,CAC3I,wKACF,CCpEA,cACE,YAAa,CACb,qBAAsB,CAEtB,WAAY,CADZ,UAEF,CAEA,wBAGE,gDAAkD,CAElD,mDAAqD,CACrD,8BAAwC,CAExC,qBAAsB,CADtB,WAAY,CAHZ,gBAAiB,CAHjB,iBAAkB,CAClB,YAOF,CAEA,eAEE,QAAO,CAEP,wBAAyB,CAHzB,iBAAkB,CAElB,UAEF,CCvBA,cAOI,kBAAmB,CAEnB,gDAAkD,CAOlD,4CAA8C,CAL9C,kBAAmB,CAEnB,8BAAwC,CAHxC,qCAAuC,CAKvC,WAAY,CAVZ,YAAa,CACb,kBAAmB,CAEnB,OAAQ,CAWR,WAAY,CAFZ,SAAU,CALV,eAAgB,CAXhB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAYT,6DAAiE,CAIjE,iBAAkB,CAdlB,YAgBJ,CAEA,mCACI,SAAU,CACV,kBACJ,CAEA,qBACI,eACJ,CAEA,gBAQI,kBAAmB,CAPnB,wBAA6B,CAE7B,WAAY,CACZ,iBAAkB,CAFlB,qCAAuC,CAQvC,cAAe,CAHf,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAEvB,6CAAiD,CANjD,UAOJ,CAEA,4CAEI,+DAA0E,CAE1E,YAAa,CADb,qBAEJ,CAEA,gBAGI,kBAAmB,CADnB,YAAa,CAGb,WAAY,CACZ,WAAY,CAFZ,wBAAyB,CAHzB,iBAOJ,CAEA,+BAEI,kBAAmB,CACnB,+DAA2E,CAC3E,4CAA8C,CAC9C,iBAAkB,CAElB,qCAAuC,CANvC,YAAa,CAWb,WAAY,CADZ,0BAA2B,CAD3B,eAAgB,CAJhB,eAAgB,CAGhB,gCAAkC,CADlC,UAKJ,CAEA,qCACI,WACJ,CAEA,qBAGI,gBAAuB,CAFvB,WAAY,CAGZ,qCAAuC,CAEvC,QAAO,CADP,cAAe,CAEf,WAAY,CACZ,SAAU,CANV,YAAa,CASb,WAAY,CADZ,eAAgB,CADhB,kCAGJ,CAEA,2BACI,SACJ,CAEA,mBAII,aAAc,CAFd,WAAY,CAGZ,gBAAiB,CAJjB,UAKJ,CAEA,gCALI,qCAeJ,CAVA,aAGI,kBAAmB,CAFnB,WAAY,CACZ,YAAa,CAKb,cAAe,CAEf,WAAY,CALZ,sBAAuB,CACvB,gBAAiB,CAGjB,UAEJ,CAGA,wBAQI,gDAAkD,CAClD,8BAAwC,CAPxC,WAAY,CAEZ,sBAAuB,CADvB,eAAgB,CAEhB,SAAU,CACV,OAAQ,CALR,UAAW,CASX,YACJ,CAGA,uCACI,YACJ,CAGA,oGAEI,YACJ", "sources": ["../../../node_modules/@fontsource/source-sans-pro/index.css", "AgGrid.css", "components/GridToolBar.css"], "sourcesContent": ["/* source-sans-pro-cyrillic-ext-400-normal */\n@font-face {\n  font-family: 'Source Sans Pro';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/source-sans-pro-cyrillic-ext-400-normal.woff2) format('woff2'), url(./files/source-sans-pro-cyrillic-ext-400-normal.woff) format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n\n/* source-sans-pro-cyrillic-400-normal */\n@font-face {\n  font-family: 'Source Sans Pro';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/source-sans-pro-cyrillic-400-normal.woff2) format('woff2'), url(./files/source-sans-pro-cyrillic-400-normal.woff) format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n\n/* source-sans-pro-greek-ext-400-normal */\n@font-face {\n  font-family: 'Source Sans Pro';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/source-sans-pro-greek-ext-400-normal.woff2) format('woff2'), url(./files/source-sans-pro-greek-ext-400-normal.woff) format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n\n/* source-sans-pro-greek-400-normal */\n@font-face {\n  font-family: 'Source Sans Pro';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/source-sans-pro-greek-400-normal.woff2) format('woff2'), url(./files/source-sans-pro-greek-400-normal.woff) format('woff');\n  unicode-range: U+0370-03FF;\n}\n\n/* source-sans-pro-vietnamese-400-normal */\n@font-face {\n  font-family: 'Source Sans Pro';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/source-sans-pro-vietnamese-400-normal.woff2) format('woff2'), url(./files/source-sans-pro-vietnamese-400-normal.woff) format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;\n}\n\n/* source-sans-pro-latin-ext-400-normal */\n@font-face {\n  font-family: 'Source Sans Pro';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/source-sans-pro-latin-ext-400-normal.woff2) format('woff2'), url(./files/source-sans-pro-latin-ext-400-normal.woff) format('woff');\n  unicode-range: U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n\n/* source-sans-pro-latin-400-normal */\n@font-face {\n  font-family: 'Source Sans Pro';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url(./files/source-sans-pro-latin-400-normal.woff2) format('woff2'), url(./files/source-sans-pro-latin-400-normal.woff) format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}", ".grid-wrapper {\n  display: flex;\n  flex-direction: column; /* Stack toolbar and grid vertically */\n  width: 100%;\n  height: 100%; /* Ensure the wrapper takes the full height of the parent */\n}\n\n.grid-toolbar-container {\n  position: relative;\n  z-index: 1000;\n  background-color: var(--ag-background-color, #fff); /* Match AG Grid theme */\n  padding: 8px 10px; /* Add padding for the toolbar */\n  border-bottom: 1px solid var(--ag-border-color, #ccc); /* Add a separator */\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow */\n  height: 50px; /* Set a fixed height for the toolbar */\n  box-sizing: border-box; /* Include padding and border in the height */\n}\n\n#gridContainer {\n  position: relative;\n  flex: 1; /* Allow the grid to take the remaining space */\n  width: 100%;\n  height: calc(100% - 50px); /* Subtract the toolbar height from the total height */\n}", ".grid-toolbar {\n    position: absolute;\n    top: 10px;\n    right: 50px; /* Anchor the toolbar to the right */\n    z-index: 1000;\n    display: flex;\n    flex-direction: row; /* Normal order of buttons */\n    align-items: center;\n    gap: 2px; /* Adjust gap between elements */\n    background-color: var(--ag-background-color, #fff); /* Use AG Grid background color */\n    color: var(--ag-foreground-color, #000); /* Use AG Grid foreground color */\n    border-radius: 12px; /* Adjust border radius */\n    padding: 4px 8px; /* Small padding */\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Subtle shadow */\n    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out; /* Smooth fade-in/out */\n    cursor: grab;\n    border: 1px solid var(--ag-border-color, #ccc); /* Use AG Grid border color */\n    opacity: 0; /* Initially hidden */\n    visibility: hidden; /* Hide toolbar when not hovering */\n    height: 36px; /* Set fixed height */\n}\n\n#gridContainer:hover .grid-toolbar {\n    opacity: 1; /* Show toolbar on hover */\n    visibility: visible; /* Make toolbar visible */\n}\n\n.grid-toolbar:active {\n    cursor: grabbing;\n}\n\n.toolbar-button {\n    background-color: transparent;\n    color: var(--ag-foreground-color, #000); /* Use AG Grid foreground color */\n    border: none;\n    border-radius: 50%;\n    width: 24px; /* Adjust button size */\n    height: 24px; /* Adjust button size */\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    transition: background-color 0.2s, transform 0.2s;\n}\n\n.toolbar-button:hover,\n.toolbar-button:focus {\n    background-color: var(--ag-secondary-background-color, rgba(0, 0, 0, 0.1)); /* Use AG Grid secondary background color */\n    transform: scale(1.05);\n    outline: none;\n}\n\n.toolbar-search {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: flex-end; /* Align content to the right */\n    flex-grow: 1; /* Allow the search box to grow */\n    height: 36px; /* Match toolbar height */\n    /*  padding: 12px 10px; Increase vertical padding for larger gap */\n}\n\n.toolbar-search .toolbar-input {\n    display: flex;\n    align-items: center;\n    background-color: var(--ag-secondary-background-color, rgba(0, 0, 0, 0.05)); /* Use AG Grid secondary background color */\n    border: 1px solid var(--ag-border-color, #ccc); /* Use AG Grid border color */\n    border-radius: 8px; /* Adjust border radius */\n    padding: 2px 4px; /* Smaller padding inside the input */\n    color: var(--ag-foreground-color, #000); /* Use AG Grid foreground color */\n    width: 24px; /* Make the search box smaller */\n    transition: width 0.3s ease-in-out;\n    overflow: hidden;\n    justify-content: flex-start; /* Align content to the left */\n    height: 28px; /* Reduce height to make it smaller */\n}\n\n.toolbar-search:hover .toolbar-input {\n    width: 120px; /* Slightly expand on hover */\n}\n\n.toolbar-input input {\n    border: none;\n    outline: none;\n    background: transparent;\n    color: var(--ag-foreground-color, #000); /* Use AG Grid foreground color */\n    font-size: 12px; /* Keep the font size small */\n    flex: 1;\n    min-width: 0; /* Prevent input from forcing width */\n    opacity: 0; /* Initially hidden */\n    transition: opacity 0.3s ease-in-out;\n    text-align: left; /* Align text to the left */\n    padding: 2px; /* Add padding inside the input */\n}\n\n.toolbar-input:hover input {\n    opacity: 1; /* Show input on hover */\n}\n\n.toolbar-input svg {\n    width: 14px; /* Adjust icon size */\n    height: 14px; /* Adjust icon size */\n    color: var(--ag-foreground-color, #000); /* Use AG Grid foreground color */\n    flex-shrink: 0;\n    margin-right: 4px; /* Adjust spacing between the icon and input */\n}\n\n.drag-handle {\n    cursor: grab;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-left: -6px; /* Adjust spacing for compact layout */\n    color: var(--ag-foreground-color, #000); /* Use AG Grid foreground color */\n    font-size: 14px; /* Adjust font size */\n    width: 30px; /* Match toolbar height */\n    height: 30px; /* Match toolbar height */\n}\n\n/* Collapsed toolbar styles */\n.grid-toolbar.collapsed {\n    width: 36px; /* Shrink toolbar to the size of the toggle button */\n    height: 36px; /* Keep height consistent */\n    overflow: hidden; /* Hide content */\n    justify-content: center; /* Center the toggle button */\n    padding: 0; /* Remove padding */\n    right: 0; /* Position the toolbar on the far right */\n    /* border-radius: 50%; Make it circular */\n    background-color: var(--ag-background-color, #fff); /* Match background color */\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Subtle shadow */\n    z-index: 1001; /* Ensure it appears above other elements */\n}\n\n/* Ensure only the toggle button is visible when collapsed */\n.grid-toolbar.collapsed .toggle-button {\n    display: flex; /* Ensure the toggle button is visible */\n}\n\n/* Hide all other buttons when collapsed */\n.grid-toolbar.collapsed .toolbar-button:not(.toggle-button),\n.grid-toolbar.collapsed .toolbar-search {\n    display: none;\n}\n"], "names": [], "sourceRoot": ""}