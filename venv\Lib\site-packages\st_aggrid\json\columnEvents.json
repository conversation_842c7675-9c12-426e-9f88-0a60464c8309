[{"name": "filterActiveChanged", "props": {"Type": "ColumnEvent"}, "description": "The filter active value has changed.More details "}, {"name": "sortChanged", "props": {"Type": "ColumnEvent"}, "description": "The sort value has changed.More details "}, {"name": "leftChanged", "props": {"Type": "ColumnEvent"}, "description": "The left position has changed (e.g. column has moved).More details "}, {"name": "movingChanged", "props": {"Type": "ColumnEvent"}, "description": "The column has started / finished moving (i.e. user is dragging the column to a new location).More details "}, {"name": "widthChanged", "props": {"Type": "ColumnEvent"}, "description": "The width value has changed.More details "}, {"name": "visibleChanged", "props": {"Type": "ColumnEvent"}, "description": "The visibility value has changed.More details "}, {"name": "menuVisibleChanged", "props": {"Type": "ColumnEvent"}, "description": "The column menu was shown / hidden.More details "}, {"name": "columnRowGroupChanged", "props": {"Type": "ColumnEvent"}, "description": "The row group value has changed.More details "}, {"name": "columnPivotChanged", "props": {"Type": "ColumnEvent"}, "description": "The pivot value has changed.More details "}, {"name": "columnValueChanged", "props": {"Type": "ColumnEvent"}, "description": "The 'value' value has changed.More details "}]