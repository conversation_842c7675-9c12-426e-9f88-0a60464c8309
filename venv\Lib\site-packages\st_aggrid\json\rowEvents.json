[{"name": "rowSelected", "props": {"Type": "RowNodeEvent"}, "description": "Row was selected or unselected.More details "}, {"name": "mouseEnter", "props": {"Type": "RowNodeEvent"}, "description": "Mouse has entered the row.More details "}, {"name": "mouseLeave", "props": {"Type": "RowNodeEvent"}, "description": "Mouse has left the row.More details "}, {"name": "cellChanged", "props": {"Type": "CellChangedEvent"}, "description": "One cell value has changed.More details "}, {"name": "dataChanged", "props": {"Type": "DataChangedEvent"}, "description": "rowData has changed.More details "}, {"name": "heightChanged", "props": {"Type": "RowNodeEvent"}, "description": "rowHeight has changed.More details "}, {"name": "rowIndexChanged", "props": {"Type": "RowNodeEvent"}, "description": "rowIndex has changed.More details "}, {"name": "topChanged", "props": {"Type": "RowNodeEvent"}, "description": "rowTop has changed.More details "}, {"name": "expandedChanged", "props": {"Type": "RowNodeEvent"}, "description": "expanded has changed.More details "}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {"Type": "RowNodeEvent"}, "description": "firstChild has changed.More details "}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props": {"Type": "RowNodeEvent"}, "description": "last<PERSON><PERSON><PERSON> has changed.More details "}, {"name": "childIndexChanged", "props": {"Type": "RowNodeEvent"}, "description": "childIndex has changed.More details "}, {"name": "allChildrenCountChanged", "props": {"Type": "RowNodeEvent"}, "description": "allChildrenCount has changed.More details "}, {"name": "uiLevelChanged", "props": {"Type": "RowNodeEvent"}, "description": "uiLevel has changed.More details "}]